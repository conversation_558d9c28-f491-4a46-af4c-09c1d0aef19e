import { NextAuthOptions, User, Account, Profile } from "next-auth";
import { MongoDBAdapter } from "@auth/mongodb-adapter";
import GitHubProvider from "next-auth/providers/github";
import GitlabProvider from "next-auth/providers/gitlab";
import { env, database } from "@/src/lib/config/environment";
import clientPromise from "@/src/lib/database/client";
import connectToDatabase from "@/src/lib/database/mongoose";
import { Organization, ProviderType, OrganizationType, InstallationStatus } from "@/src/lib/database/models";
import { ObjectId } from "mongodb";

declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      githubUsername?: string | null;
      gitlabUsername?: string | null;
    };
    // make GitLab token available on the session object
    gitlabAccessToken?: string;
    // Organization context
    organizations?: Array<{
      id: string;
      name: string;
      type: string;
      provider: string;
      installationStatus: string;
      repoCount: number;
    }>;
    currentOrganization?: {
      id: string;
      name: string;
      type: string;
      provider: string;
    };
  }
  interface JWT {
    uid?: string;
    gitlabAccessToken?: string;
    githubUsername?: string;
    gitlabUsername?: string;
    organizations?: Array<{
      id: string;
      name: string;
      type: string;
      provider: string;
      installationStatus: string;
      repoCount: number;
    }>;
    currentOrganization?: {
      id: string;
      name: string;
      type: string;
      provider: string;
    };
    organizationsUpdatedAt?: number;
  }
  interface User {
    githubUsername?: string;
    gitlabUsername?: string;
  }
}

export async function saveGitLabOAuthInfo(
  user: User,
  account: Account,
  profile?: Profile
): Promise<void> {
  try {
    // Validate required OAuth data
    if (!account.access_token) {
      throw new Error("GitLab access token is required");
    }
    if (!user.email) {
      throw new Error("User email is required for GitLab integration");
    }

    // Extract GitLab username and organization info from profile
    const gitlabUsername = (profile as any)?.username || null;

    // Fetch user's GitLab information to get organization ID
    let gitlabOrgId = null;
    if (account.access_token) {
      try {
        // Fetch user's GitLab profile to get the user ID (which serves as personal org ID)
        const userResponse = await fetch('https://gitlab.com/api/v4/user', {
          headers: {
            'Authorization': `Bearer ${account.access_token}`,
            'Accept': 'application/json',
            'User-Agent': 'Platyfend-Dashboard'
          },
        });

        if (userResponse.ok) {
          const gitlabUser = await userResponse.json();
          gitlabOrgId = gitlabUser.id?.toString(); // GitLab user ID serves as personal org ID

          console.log("GitLab user info fetched:", {
            userId: user.id,
            gitlabUserId: gitlabUser.id,
            gitlabUsername: gitlabUser.username,
            gitlabOrgId
          });
        } else {
          console.warn("Failed to fetch GitLab user info:", userResponse.status);
        }
      } catch (fetchError) {
        console.warn("Error fetching GitLab user info:", fetchError);
        // Continue without org ID - not critical for authentication
      }
    }

    // Log successful OAuth data capture (without sensitive tokens)
    console.log("GitLab OAuth info captured for user:", {
      userId: user.id,
      email: user.email,
      provider: account.provider,
      scope: account.scope,
      hasAccessToken: !!account.access_token,
      gitlabUsername,
      gitlabOrgId
    });

    // Store GitLab username in user object for session access
    if (gitlabUsername) {
      // The username will be stored in the JWT token via the jwt callback
      user.gitlabUsername = gitlabUsername;
    }

    // Update the account record with GitLab-specific information
    if (user.id && (gitlabOrgId || gitlabUsername)) {
      try {
        const client = await clientPromise;
        const db = client.db(database.databaseName);
        const accountsCollection = db.collection('accounts');

        await accountsCollection.updateOne(
          {
            userId: user.id,
            provider: 'gitlab',
            providerAccountId: account.providerAccountId
          },
          {
            $set: {
              gitlabOrgId: gitlabOrgId,
              gitlabUsername: gitlabUsername,
              updatedAt: new Date()
            }
          }
        );

        console.log("GitLab account info updated successfully:", {
          userId: user.id,
          gitlabOrgId,
          gitlabUsername
        });
      } catch (updateError) {
        console.error("Error updating GitLab account info:", updateError);
        // Don't throw - this is not critical for authentication
      }
    }

    // Store organization creation data for later processing
    // Organization will be created after user is persisted to database
    if (gitlabUsername && gitlabOrgId) {
      // Store in a temporary collection or handle via session callback
      console.log("GitLab organization creation will be handled after user persistence:", {
        gitlabUsername,
        gitlabOrgId
      });
    }

    // Skip webhook setup for localhost
    const baseUrl = process.env.NEXTAUTH_URL;
    if (baseUrl?.includes('localhost') || baseUrl?.includes('127.0.0.1')) {
      console.log("Skipping webhook setup in local development");
      return;
    }
  } catch (error) {
    console.error("Error in saveGitLabOAuthInfo:", error);
    throw error;
  }
}

/**
 * Saves GitHub OAuth information and creates initial organization record
 * This function handles the OAuth data received during GitHub authentication
 */
export async function saveGitHubOAuthInfo(
  user: User,
  account: Account,
  profile?: Profile
): Promise<void> {
  try {
    // Validate required OAuth data
    if (!account.access_token) {
      throw new Error("GitHub access token is required");
    }

    if (!user.email) {
      throw new Error("User email is required for GitHub integration");
    }

    // Extract GitHub username and organization info from profile
    const githubUsername = (profile as any)?.login || null;

    // Fetch user's GitHub information to get organization ID
    let githubOrgId = null;
    if (account.access_token) {
      try {
        // Fetch user's GitHub profile to get the user ID (which serves as personal org ID)
        const userResponse = await fetch('https://api.github.com/user', {
          headers: {
            'Authorization': `Bearer ${account.access_token}`,
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Platyfend-Dashboard'
          },
        });

        if (userResponse.ok) {
          const githubUser = await userResponse.json();
          githubOrgId = githubUser.id?.toString(); // GitHub user ID serves as personal org ID

          console.log("GitHub user info fetched:", {
            userId: user.id,
            githubUserId: githubUser.id,
            githubUsername: githubUser.login,
            githubOrgId
          });
        } else {
          console.warn("Failed to fetch GitHub user info:", userResponse.status);
        }
      } catch (fetchError) {
        console.warn("Error fetching GitHub user info:", fetchError);
        // Continue without org ID - not critical for authentication
      }
    }

    // Log successful OAuth data capture (without sensitive tokens)
    console.log("GitHub OAuth info captured for user:", {
      userId: user.id,
      email: user.email,
      provider: account.provider,
      scope: account.scope,
      hasAccessToken: !!account.access_token,
      githubUsername,
      githubOrgId
    });

    // Store GitHub username in user object for session access
    if (githubUsername) {
      // The username will be stored in the JWT token via the jwt callback
      user.githubUsername = githubUsername;
    }
    // Update the account record with GitHub-specific information
    if (user.id && (githubOrgId || githubUsername)) {
      try {
        const client = await clientPromise;
        const db = client.db(database.databaseName);
        const accountsCollection = db.collection('accounts');

        await accountsCollection.updateOne(
          {
            userId: user.id,
            provider: 'github',
            providerAccountId: account.providerAccountId
          },
          {
            $set: {
              githubOrgId: githubOrgId,
              githubUsername: githubUsername,
              updatedAt: new Date()
            }
          }
        );

        console.log("GitHub account info updated successfully:", {
          userId: user.id,
          githubOrgId,
          githubUsername
        });
      } catch (updateError) {
        console.error("Error updating GitHub account info:", updateError);
        // Don't throw - this is not critical for authentication
      }
    }

    // Create initial personal organization record for the user
    // This will have 0 repositories initially - repositories will be added via GitHub App installation
    await createInitialOrganization(user, githubUsername, githubOrgId);

  } catch (error) {
    console.error("Error in saveGitHubOAuthInfo:", error);
    throw error;
  }
}

/**
 * Creates initial personal organization record for GitHub user
 * Repositories will be added later via GitHub App installation
 */
async function createInitialOrganization(
  user: User,
  githubUsername: string | null,
  githubOrgId: string | null
): Promise<void> {
  try {
    if (!githubUsername || !githubOrgId) {
      console.log("Missing GitHub username or org ID, skipping organization creation");
      return;
    }

    // Connect to database
    await connectToDatabase();

    // Get the MongoDB user document to ensure we have the correct _id
    const client = await clientPromise;
    const db = client.db(database.databaseName);
    const usersCollection = db.collection('users');

    // Debug: Log what we're searching for
    console.log(`🔍 Searching for user with email: ${user.email} and id: ${user.id}`);
    console.log(`🔍 Using database: ${database.databaseName}`);

    // Debug: Check what collections exist and what users are in the database
    try {
      const collections = await db.listCollections().toArray();
      console.log(`📋 Available collections:`, collections.map(c => c.name));

      // Check both 'users' and 'User' collections (NextAuth might use capitalized)
      const userCollections = ['users', 'User'];
      for (const collName of userCollections) {
        try {
          const collection = db.collection(collName);
          const count = await collection.countDocuments();
          console.log(`📊 Collection '${collName}' has ${count} documents`);

          if (count > 0) {
            // Show a sample document structure
            const sample = await collection.findOne({});
            console.log(`📄 Sample document from '${collName}':`, JSON.stringify(sample, null, 2));
          }
        } catch (err) {
          console.log(`❌ Error checking collection '${collName}':`, err);
        }
      }
    } catch (err) {
      console.log(`❌ Error listing collections:`, err);
    }

    // Since this is called from NextAuth signIn callback, we should use the user's email
    // NextAuth MongoDB adapter creates users with email as the primary identifier

    let userDoc;

    // Try both 'users' and 'User' collections
    const collectionsToTry = ['users', 'User'];

    for (const collectionName of collectionsToTry) {
      const collection = db.collection(collectionName);

      // First, try to find by email (most reliable for NextAuth)
      if (user.email && !userDoc) {
        console.log(`Trying email lookup for: ${user.email} in collection: ${collectionName}`);

        // Try exact match first
        userDoc = await collection.findOne({ email: user.email });
        if (userDoc) {
          console.log(`✅ Found user by email in '${collectionName}': ${userDoc._id}`);
          break;
        }

        // Try case-insensitive match
        userDoc = await collection.findOne({
          email: { $regex: new RegExp(`^${user.email.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') }
        });
        if (userDoc) {
          console.log(`✅ Found user by case-insensitive email in '${collectionName}': ${userDoc._id}`);
          break;
        }
      }

      // If not found by email, try by user.id if it's a valid ObjectId
      if (!userDoc && user.id && ObjectId.isValid(user.id)) {
        try {
          console.log(`Trying ObjectId lookup for: ${user.id} in collection: ${collectionName}`);
          userDoc = await collection.findOne({ _id: new ObjectId(user.id) });
          if (userDoc) {
            console.log(`✅ Found user by ObjectId in '${collectionName}': ${userDoc._id}`);
            break;
          }
        } catch (error) {
          console.warn(`Failed to query user by ObjectId in '${collectionName}': ${error}`);
        }
      }
    }

    // If still not found, this might be the first login - skip organization creation
    if (!userDoc) {
      console.log(`User not found in database yet. This might be the first login.`);
      console.log(`Skipping organization creation for now - it will be created after user is persisted.`);
      return;
    }

    const mongoUserId = userDoc._id.toString();

    // Check if organization already exists
    const existingOrg = await Organization.findOne({
      user_id: mongoUserId,
      org_id: githubOrgId,
      provider: ProviderType.GITHUB
    });

    if (existingOrg) {
      console.log(`Organization already exists for user ${mongoUserId} and GitHub org ${githubOrgId}`);
      return;
    }

    // Create new personal organization record
    const organization = new Organization({
      org_id: githubOrgId,
      user_id: mongoUserId, // Use the MongoDB _id from users collection
      provider: ProviderType.GITHUB,
      org_name: githubUsername,
      org_type: OrganizationType.PERSONAL,
      installation_id: null, // Will be set when GitHub App is installed
      installation_status: InstallationStatus.PENDING,
      permissions: {},
      repos: [], // Start with 0 repositories
      created_at: new Date(),
      updated_at: new Date()
    });

    const savedOrg = await organization.save();

    console.log(`Created initial organization for user ${mongoUserId}:`, {
      orgId: githubOrgId,
      orgName: githubUsername,
      repoCount: 0,
      savedOrgId: savedOrg._id,
      savedOrgOrgId: savedOrg.org_id
    });

    // Verify the organization was saved by querying it back
    const verifyOrg = await Organization.findOne({
      user_id: mongoUserId,
      org_id: githubOrgId,
      provider: ProviderType.GITHUB
    });

    if (verifyOrg) {
      console.log(`✅ Verified GitHub organization was saved successfully:`, {
        _id: verifyOrg._id,
        org_id: verifyOrg.org_id,
        org_name: verifyOrg.org_name,
        provider: verifyOrg.provider
      });
    } else {
      console.error(`❌ Failed to verify GitHub organization was saved for user ${mongoUserId}`);
    }

  } catch (error: any) {
    console.error("Error creating initial organization:", error);
    if (error.errors) {
      console.error("Validation errors:", Object.keys(error.errors).map(key => ({
        field: key,
        message: error.errors[key].message,
        value: error.errors[key].value
      })));
    }
    // Don't throw - this is not critical for authentication
  }
}



/**
 * Ensures GitLab organization exists for a user after they've been persisted
 * This is called from the JWT callback after user creation
 */
async function ensureGitLabOrganization(
  userId: string,
  gitlabUsername: string
): Promise<void> {
  try {
    await connectToDatabase();

    // Check if organization already exists
    const existingOrg = await Organization.findOne({
      user_id: userId,
      provider: ProviderType.GITLAB
    });

    if (existingOrg) {
      console.log(`GitLab organization already exists for user ${userId}`);
      return;
    }

    // Get GitLab org ID from account record
    const client = await clientPromise;
    const db = client.db(database.databaseName);
    const accountsCollection = db.collection('accounts');

    const gitlabAccount = await accountsCollection.findOne({
      userId: userId,
      provider: 'gitlab'
    });

    if (!gitlabAccount?.gitlabOrgId) {
      console.log(`No GitLab org ID found for user ${userId}, skipping organization creation`);
      return;
    }

    // Create new personal organization record
    const organization = new Organization({
      org_id: gitlabAccount.gitlabOrgId,
      user_id: userId,
      provider: ProviderType.GITLAB,
      org_name: gitlabUsername,
      org_type: OrganizationType.PERSONAL,
      installation_id: null,
      installation_status: InstallationStatus.ACTIVE,
      permissions: {},
      repos: [],
      created_at: new Date(),
      updated_at: new Date()
    });

    const savedOrg = await organization.save();

    console.log(`✅ Created GitLab organization for user ${userId}:`, {
      orgId: gitlabAccount.gitlabOrgId,
      orgName: gitlabUsername,
      savedOrgId: savedOrg._id
    });

  } catch (error) {
    console.error("Error ensuring GitLab organization:", error);
    // Don't throw - this shouldn't break authentication
  }
}

export const authOptions: NextAuthOptions = {
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    GitHubProvider({
      clientId: env.GITHUB_CLIENT_ID,
      clientSecret: env.GITHUB_CLIENT_SECRET,
      authorization: {
        params: {
          scope: "user:email read:org",
        },
      },
    }),
    // Only include GitLab provider if credentials are available
    ...(env.GITLAB_CLIENT_ID && env.GITLAB_CLIENT_SECRET ? [
      GitlabProvider({
        clientId: env.GITLAB_CLIENT_ID,
        clientSecret: env.GITLAB_CLIENT_SECRET,
        authorization: {
          params: {
            scope: "read_user read_api read_repository api",
          },
        },
      })
    ] : []),
    // Add more providers as needed
    // AzureADProvider, etc.
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      // Validate that we have the required authentication data
      if (!user || !account) {
        console.error("Missing user or account data in signIn callback");
        return false;
      }

      // Handle GitHub OAuth specifically
      if (account.provider === "github" && account.access_token) {
        try {
          // Save GitHub OAuth information for workspace integration
          await saveGitHubOAuthInfo(user, account, profile);
        } catch (error) {
          console.error("Error saving GitHub OAuth info:", error);
          
        }
      } else if (account.provider === "gitlab" && account.access_token) {
        // Handle GitLab OAuth
        try {
          await saveGitLabOAuthInfo(user, account, profile);
        } catch (error) {
          console.error("Error saving GitLab OAuth info:", error);
        } 
      }

      // Additional validation can be added here for other providers
      // For example: email domain restrictions, organization membership, etc.

      return true;
    },
    session: async ({ session, token }) => {
      if (session?.user) {
        session.user.id = token.sub!;
        session.user.githubUsername = (token.githubUsername as string) || null;
        session.user.gitlabUsername = (token.gitlabUsername as string) || null;

        // Add organization context to session
        session.organizations = token.organizations as any;
        session.currentOrganization = token.currentOrganization as any;
      }
      return session;
    },
    jwt: async ({ user, token, account }) => {
      if (user) {
        token.uid = user.id;
        if (user.githubUsername) {
          token.githubUsername = user.githubUsername;
        }
        if (user.gitlabUsername) {
          token.gitlabUsername = user.gitlabUsername;
        }

        // Handle delayed GitLab organization creation
        if (account?.provider === 'gitlab' && user.gitlabUsername && user.id) {
          try {
            await ensureGitLabOrganization(user.id, user.gitlabUsername);
          } catch (error) {
            console.error("Error creating GitLab organization in JWT callback:", error);
          }
        }
      }

      // Refresh organization data only when needed (e.g., on login or every N minutes)
      const shouldRefreshOrgs = user || !token.organizations ||
        (token.organizationsUpdatedAt && Date.now() - (token.organizationsUpdatedAt as number) > 5 * 60 * 1000);

      if (token.uid && shouldRefreshOrgs) {
        try {
          const organizations = await getUserOrganizations(token.uid as string);
          token.organizations = organizations;
          token.organizationsUpdatedAt = Date.now();

          // Set current organization (first active one or first one)
          const currentOrg = organizations.find(org => org.installationStatus === 'active') || organizations[0];
          token.currentOrganization = currentOrg ? {
            id: currentOrg.id,
            name: currentOrg.name,
            type: currentOrg.type,
            provider: currentOrg.provider
          } : undefined;
        } catch (error) {
          console.error("Error loading organizations in JWT callback:", error);
          // Don't fail authentication if organization loading fails
          token.organizations = [];
          token.currentOrganization = undefined;
        }
      }
      return token;
    },
    redirect: async ({ url, baseUrl }) => {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url;
      // Default redirect to dashboard after successful login
      return `${baseUrl}/dashboard`;
    },
  },
  session: {
    strategy: "jwt",
  },
  pages: {
    signIn: "/login",
    error: "/auth/error",
  },
};

/**
 * Get user's organizations for session context
 */
async function getUserOrganizations(userId: string) {
  try {
    const organizations = await Organization.find({
      user_id: userId
    }).sort({ created_at: -1 });

    return organizations.map(org => ({
      id: org.org_id, // Use external org_id instead of MongoDB _id
      name: org.org_name,
      type: org.org_type,
      provider: org.provider,
      installationStatus: org.installation_status,
      repoCount: org.repos.length
    }));
  } catch (error) {
    console.error("Error fetching user organizations:", error);
    return [];
  }
}