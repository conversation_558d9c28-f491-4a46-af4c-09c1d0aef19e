import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/src/features/auth/lib/auth-config';
import { gitlabTokenAuth } from '@/src/lib/gitlab/token-auth';

/**
 * POST /api/gitlab/token/[userId]/validate
 * Validate GitLab access token
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { userId } = params;

    // Only allow users to validate their own tokens
    if (session.user.id !== userId) {
      return NextResponse.json(
        { error: 'Forbidden - can only validate your own tokens' },
        { status: 403 }
      );
    }

    // Get the current token and validate it
    const token = await gitlabTokenAuth.getValidAccessToken(userId);
    const isValid = await gitlabTokenAuth.validateToken(token);

    if (isValid) {
      // Get user info to provide additional context
      const userInfo = await gitlabTokenAuth.getGitLabUser(token);
      
      return NextResponse.json({
        success: true,
        valid: true,
        user_id: userId,
        provider: 'gitlab',
        gitlab_user: {
          id: userInfo.id,
          username: userInfo.username,
          name: userInfo.name,
          email: userInfo.email
        },
        validated_at: new Date().toISOString(),
        message: 'GitLab token is valid'
      });
    } else {
      return NextResponse.json({
        success: true,
        valid: false,
        user_id: userId,
        provider: 'gitlab',
        validated_at: new Date().toISOString(),
        message: 'GitLab token is invalid or expired',
        requiresGitLabAuth: true
      });
    }

  } catch (error) {
    console.error('Error validating GitLab token:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No GitLab account found')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'No GitLab account linked',
            message: 'Please link your GitLab account first',
            requiresGitLabAuth: true
          },
          { status: 404 }
        );
      }
      
      if (error.message.includes('No access token found')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'No access token available',
            message: 'GitLab account exists but no access token found',
            requiresGitLabAuth: true
          },
          { status: 404 }
        );
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to validate GitLab token',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
