import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/src/features/auth/lib/auth-config';
import { gitlabTokenAuth } from '@/src/lib/gitlab/token-auth';

/**
 * GET /api/gitlab/token/[userId]
 * Get valid GitLab access token for a user (refreshes if expired)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { userId } = params;

    // Only allow users to access their own tokens or admin access
    if (session.user.id !== userId) {
      return NextResponse.json(
        { error: 'Forbidden - can only access your own tokens' },
        { status: 403 }
      );
    }

    // Get valid token (will refresh if expired)
    const token = await gitlabTokenAuth.getValidAccessToken(userId);

    return NextResponse.json({
      success: true,
      user_id: userId,
      token: token,
      provider: 'gitlab',
      generated_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting GitLab token:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No GitLab account found')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'No GitLab account linked',
            message: 'Please link your GitLab account first',
            requiresGitLabAuth: true
          },
          { status: 404 }
        );
      }
      
      if (error.message.includes('No access token found')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'No access token available',
            message: 'GitLab account exists but no access token found',
            requiresGitLabAuth: true
          },
          { status: 404 }
        );
      }
      
      if (error.message.includes('No refresh token available')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Token expired and cannot be refreshed',
            message: 'Please re-authenticate with GitLab',
            requiresGitLabAuth: true
          },
          { status: 401 }
        );
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to get GitLab token',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/gitlab/token/[userId]
 * Force refresh GitLab access token
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { userId } = params;

    // Only allow users to refresh their own tokens
    if (session.user.id !== userId) {
      return NextResponse.json(
        { error: 'Forbidden - can only refresh your own tokens' },
        { status: 403 }
      );
    }

    // Force refresh token
    const newToken = await gitlabTokenAuth.regenerateAccessToken(userId);

    return NextResponse.json({
      success: true,
      user_id: userId,
      token: newToken,
      provider: 'gitlab',
      refreshed_at: new Date().toISOString(),
      message: 'GitLab token successfully refreshed'
    });

  } catch (error) {
    console.error('Error refreshing GitLab token:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('No GitLab account found')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'No GitLab account linked',
            message: 'Please link your GitLab account first'
          },
          { status: 404 }
        );
      }
      
      if (error.message.includes('No refresh token available')) {
        return NextResponse.json(
          { 
            success: false,
            error: 'Cannot refresh token',
            message: 'No refresh token available. Please re-authenticate with GitLab',
            requiresGitLabAuth: true
          },
          { status: 401 }
        );
      }
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to refresh GitLab token',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
