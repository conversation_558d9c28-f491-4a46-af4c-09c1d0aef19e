import { NextRequest, NextResponse } from 'next/server';
import { requireGitLabAuth } from '@/src/lib/gitlab/middleware';
import { createGitLabClient } from '@/src/lib/gitlab/api-client';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/src/features/auth/lib/auth-config';

/**
 * GET /api/gitlab/projects
 * Get user's GitLab projects with automatic token management
 */
async function getGitLabProjects(request: NextRequest, gitlabClient: any): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const owned = searchParams.get('owned') === 'true';
    const membership = searchParams.get('membership') === 'true';
    const starred = searchParams.get('starred') === 'true';
    const simple = searchParams.get('simple') === 'true';
    const per_page = searchParams.get('per_page') ? parseInt(searchParams.get('per_page')!) : 20;
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1;

    // Create GitLab API client
    const client = createGitLabClient(session.user.id);

    // Fetch projects
    const projects = await client.getProjects({
      owned,
      membership,
      starred,
      simple,
      per_page,
      page
    });

    return NextResponse.json({
      success: true,
      projects,
      pagination: {
        page,
        per_page,
        total: projects.length
      },
      fetched_at: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error fetching GitLab projects:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('401') || error.message.includes('authentication')) {
        return NextResponse.json(
          {
            error: 'GitLab authentication failed',
            message: 'Please re-authenticate with GitLab',
            requiresGitLabAuth: true
          },
          { status: 401 }
        );
      }
      
      if (error.message.includes('403')) {
        return NextResponse.json(
          {
            error: 'GitLab access forbidden',
            message: 'Insufficient permissions to access GitLab projects'
          },
          { status: 403 }
        );
      }
    }
    
    return NextResponse.json(
      {
        error: 'Failed to fetch GitLab projects',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Export the handler wrapped with GitLab authentication
export const GET = requireGitLabAuth(getGitLabProjects);
