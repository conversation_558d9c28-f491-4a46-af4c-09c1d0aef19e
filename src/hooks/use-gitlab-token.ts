import { useState, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface GitLabTokenResponse {
  success: boolean;
  token?: string;
  user_id?: string;
  provider?: string;
  error?: string;
  message?: string;
  requiresGitLabAuth?: boolean;
  gitlab_user?: {
    id: number;
    username: string;
    name: string;
    email: string;
  };
}

interface UseGitLabTokenReturn {
  getToken: () => Promise<string | null>;
  refreshToken: () => Promise<string | null>;
  validateToken: () => Promise<boolean>;
  isLoading: boolean;
  error: string | null;
  requiresAuth: boolean;
}

/**
 * Custom hook for managing GitLab access tokens
 * Handles token retrieval, refresh, and validation
 */
export function useGitLabToken(): UseGitLabTokenReturn {
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [requiresAuth, setRequiresAuth] = useState(false);

  const handleResponse = useCallback((response: GitLabTokenResponse) => {
    if (response.requiresGitLabAuth) {
      setRequiresAuth(true);
    }
    
    if (!response.success) {
      setError(response.message || response.error || 'Unknown error');
      return null;
    }
    
    setError(null);
    setRequiresAuth(false);
    return response.token || null;
  }, []);

  const getToken = useCallback(async (): Promise<string | null> => {
    if (!session?.user?.id) {
      setError('User not authenticated');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/gitlab/token/${session.user.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data: GitLabTokenResponse = await response.json();
      return handleResponse(data);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get GitLab token';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id, handleResponse]);

  const refreshToken = useCallback(async (): Promise<string | null> => {
    if (!session?.user?.id) {
      setError('User not authenticated');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/gitlab/token/${session.user.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data: GitLabTokenResponse = await response.json();
      return handleResponse(data);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh GitLab token';
      setError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id, handleResponse]);

  const validateToken = useCallback(async (): Promise<boolean> => {
    if (!session?.user?.id) {
      setError('User not authenticated');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/gitlab/token/${session.user.id}/validate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data: GitLabTokenResponse = await response.json();
      
      if (data.requiresGitLabAuth) {
        setRequiresAuth(true);
      }
      
      if (!data.success) {
        setError(data.message || data.error || 'Token validation failed');
        return false;
      }
      
      setError(null);
      setRequiresAuth(false);
      return data.valid !== false; // Default to true if valid field is not present

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to validate GitLab token';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [session?.user?.id]);

  return {
    getToken,
    refreshToken,
    validateToken,
    isLoading,
    error,
    requiresAuth,
  };
}
