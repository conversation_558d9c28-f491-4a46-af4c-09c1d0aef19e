import { connectToDatabase } from '@/src/lib/database/mongoose';
import clientPromise from '@/src/lib/database/client';
import { database } from '@/src/lib/config/database';

/**
 * GitLab OAuth Token Management Service
 * Handles token validation, refresh, and API client creation for GitLab
 */
export class GitLabTokenAuth {
  private clientId: string;
  private clientSecret: string;

  constructor() {
    this.clientId = process.env.GITLAB_CLIENT_ID!;
    this.clientSecret = process.env.GITLAB_CLIENT_SECRET!;

    if (!this.clientId || !this.clientSecret) {
      throw new Error('GitLab client credentials not configured. Please set GITLAB_CLIENT_ID and GITLAB_CLIENT_SECRET environment variables.');
    }
  }

  /**
   * Get valid GitLab access token for a user, refreshing if necessary
   */
  async getValidAccessToken(userId: string): Promise<string> {
    await connectToDatabase();

    const client = await clientPromise;
    const db = client.db(database.databaseName);
    const accountsCollection = db.collection('accounts');

    // Find GitLab account for the user
    const account = await accountsCollection.findOne({
      userId: userId,
      provider: 'gitlab'
    });

    if (!account) {
      throw new Error('No GitLab account found for user');
    }

    if (!account.access_token) {
      throw new Error('No access token found for GitLab account');
    }

    // Check if token is expired (with 5 minute buffer)
    const now = Math.floor(Date.now() / 1000);
    const expiresAt = account.expires_at;

    if (expiresAt && expiresAt <= (now + 300)) { // 5 minute buffer
      console.log('GitLab token expired, attempting refresh...');
      return await this.refreshAccessToken(userId, account);
    }

    return account.access_token;
  }

  /**
   * Refresh GitLab access token using refresh token
   */
  async refreshAccessToken(userId: string, account: any): Promise<string> {
    if (!account.refresh_token) {
      throw new Error('No refresh token available for GitLab account');
    }

    try {
      const response = await fetch('https://gitlab.com/oauth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
        },
        body: new URLSearchParams({
          grant_type: 'refresh_token',
          refresh_token: account.refresh_token,
          client_id: this.clientId,
          client_secret: this.clientSecret,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('GitLab token refresh failed:', response.status, errorText);
        throw new Error(`GitLab token refresh failed: ${response.status} ${errorText}`);
      }

      const tokenData = await response.json();

      // Update account with new tokens
      const client = await clientPromise;
      const db = client.db(database.databaseName);
      const accountsCollection = db.collection('accounts');

      const updateData: any = {
        access_token: tokenData.access_token,
        updatedAt: new Date(),
      };

      // Update refresh token if provided
      if (tokenData.refresh_token) {
        updateData.refresh_token = tokenData.refresh_token;
      }

      // Update expires_at if provided
      if (tokenData.expires_in) {
        updateData.expires_at = Math.floor(Date.now() / 1000) + tokenData.expires_in;
      }

      await accountsCollection.updateOne(
        {
          userId: userId,
          provider: 'gitlab',
          providerAccountId: account.providerAccountId
        },
        {
          $set: updateData
        }
      );

      console.log('GitLab token refreshed successfully for user:', userId);
      return tokenData.access_token;

    } catch (error) {
      console.error('Error refreshing GitLab token:', error);
      throw new Error(`Failed to refresh GitLab token: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate GitLab access token by making a test API call
   */
  async validateToken(accessToken: string): Promise<boolean> {
    try {
      const response = await fetch('https://gitlab.com/api/v4/user', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
          'User-Agent': 'Platyfend-Dashboard'
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Error validating GitLab token:', error);
      return false;
    }
  }

  /**
   * Create authenticated API client for GitLab
   */
  async createAPIClient(userId: string) {
    const token = await this.getValidAccessToken(userId);
    
    return {
      token,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'application/json',
        'User-Agent': 'Platyfend-Dashboard',
      },
    };
  }

  /**
   * Force regenerate GitLab access token
   */
  async regenerateAccessToken(userId: string): Promise<string> {
    await connectToDatabase();

    const client = await clientPromise;
    const db = client.db(database.databaseName);
    const accountsCollection = db.collection('accounts');

    // Find GitLab account for the user
    const account = await accountsCollection.findOne({
      userId: userId,
      provider: 'gitlab'
    });

    if (!account) {
      throw new Error('No GitLab account found for user');
    }

    // Force refresh the token
    return await this.refreshAccessToken(userId, account);
  }

  /**
   * Get GitLab user information using access token
   */
  async getGitLabUser(accessToken: string): Promise<any> {
    try {
      const response = await fetch('https://gitlab.com/api/v4/user', {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Accept': 'application/json',
          'User-Agent': 'Platyfend-Dashboard'
        },
      });

      if (!response.ok) {
        throw new Error(`GitLab API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching GitLab user:', error);
      throw error;
    }
  }
}

// Singleton instance
export const gitlabTokenAuth = new GitLabTokenAuth();

// Helper functions for common operations
export async function getGitLabAccessToken(userId: string): Promise<string> {
  return gitlabTokenAuth.getValidAccessToken(userId);
}

export async function validateGitLabToken(accessToken: string): Promise<boolean> {
  return gitlabTokenAuth.validateToken(accessToken);
}

export async function refreshGitLabToken(userId: string): Promise<string> {
  return gitlabTokenAuth.regenerateAccessToken(userId);
}
