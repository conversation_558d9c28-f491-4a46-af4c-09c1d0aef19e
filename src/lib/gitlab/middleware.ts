import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/src/features/auth/lib/auth-config';
import { gitlabTokenAuth } from './token-auth';

/**
 * GitLab authentication middleware for API routes
 * Ensures user has valid GitLab token before proceeding
 */
export async function withGitLabAuth(
  request: NextRequest,
  handler: (request: NextRequest, gitlabClient: any) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized - please log in' },
        { status: 401 }
      );
    }

    // Get valid GitLab token (will refresh if expired)
    let token: string;
    try {
      token = await gitlabTokenAuth.getValidAccessToken(session.user.id);
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('No GitLab account found')) {
          return NextResponse.json(
            {
              error: 'No GitLab account linked',
              message: 'Please link your GitLab account to access this feature',
              requiresGitLabAuth: true
            },
            { status: 404 }
          );
        }
        
        if (error.message.includes('No access token found')) {
          return NextResponse.json(
            {
              error: 'No GitLab access token',
              message: 'GitLab account exists but no access token found',
              requiresGitLabAuth: true
            },
            { status: 404 }
          );
        }
        
        if (error.message.includes('No refresh token available')) {
          return NextResponse.json(
            {
              error: 'GitLab token expired',
              message: 'Token expired and cannot be refreshed. Please re-authenticate with GitLab',
              requiresGitLabAuth: true
            },
            { status: 401 }
          );
        }
      }
      
      return NextResponse.json(
        {
          error: 'GitLab authentication failed',
          message: error instanceof Error ? error.message : 'Unknown error',
          requiresGitLabAuth: true
        },
        { status: 500 }
      );
    }

    // Create GitLab API client
    const gitlabClient = await gitlabTokenAuth.createAPIClient(session.user.id);

    // Call the actual handler with the authenticated client
    return await handler(request, gitlabClient);

  } catch (error) {
    console.error('GitLab middleware error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

/**
 * Higher-order function to wrap API route handlers with GitLab authentication
 */
export function requireGitLabAuth(
  handler: (request: NextRequest, gitlabClient: any) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    return withGitLabAuth(request, handler);
  };
}

/**
 * Utility function to check if user has valid GitLab authentication
 */
export async function checkGitLabAuth(userId: string): Promise<{
  isAuthenticated: boolean;
  error?: string;
  requiresAuth?: boolean;
}> {
  try {
    await gitlabTokenAuth.getValidAccessToken(userId);
    return { isAuthenticated: true };
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('No GitLab account found') ||
          error.message.includes('No access token found') ||
          error.message.includes('No refresh token available')) {
        return {
          isAuthenticated: false,
          error: error.message,
          requiresAuth: true
        };
      }
    }
    
    return {
      isAuthenticated: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Utility function to validate GitLab token and get user info
 */
export async function validateGitLabAuthAndGetUser(userId: string): Promise<{
  isValid: boolean;
  user?: any;
  error?: string;
  requiresAuth?: boolean;
}> {
  try {
    const token = await gitlabTokenAuth.getValidAccessToken(userId);
    const isValid = await gitlabTokenAuth.validateToken(token);
    
    if (!isValid) {
      return {
        isValid: false,
        error: 'GitLab token is invalid',
        requiresAuth: true
      };
    }
    
    const user = await gitlabTokenAuth.getGitLabUser(token);
    return {
      isValid: true,
      user
    };
    
  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('No GitLab account found') ||
          error.message.includes('No access token found') ||
          error.message.includes('No refresh token available')) {
        return {
          isValid: false,
          error: error.message,
          requiresAuth: true
        };
      }
    }
    
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
