import { gitlabTokenAuth } from './token-auth';

/**
 * GitLab API Client with automatic token management
 * Handles token refresh and provides convenient methods for GitLab API calls
 */
export class GitLabAPIClient {
  private userId: string;
  private baseUrl: string = 'https://gitlab.com/api/v4';

  constructor(userId: string) {
    this.userId = userId;
  }

  /**
   * Make authenticated request to GitLab API
   */
  private async makeRequest(endpoint: string, options: RequestInit = {}): Promise<Response> {
    const client = await gitlabTokenAuth.createAPIClient(this.userId);
    
    const url = endpoint.startsWith('http') ? endpoint : `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...client.headers,
        ...options.headers,
      },
    });

    // If token is invalid, try to refresh and retry once
    if (response.status === 401) {
      console.log('GitLab API returned 401, attempting token refresh...');
      
      try {
        await gitlabTokenAuth.regenerateAccessToken(this.userId);
        const newClient = await gitlabTokenAuth.createAPIClient(this.userId);
        
        const retryResponse = await fetch(url, {
          ...options,
          headers: {
            ...newClient.headers,
            ...options.headers,
          },
        });
        
        return retryResponse;
      } catch (refreshError) {
        console.error('Failed to refresh GitLab token:', refreshError);
        throw new Error('GitLab authentication failed and token refresh unsuccessful');
      }
    }

    return response;
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<any> {
    const response = await this.makeRequest('/user');
    
    if (!response.ok) {
      throw new Error(`Failed to get user info: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Get user's projects/repositories
   */
  async getProjects(options: {
    owned?: boolean;
    membership?: boolean;
    starred?: boolean;
    simple?: boolean;
    per_page?: number;
    page?: number;
  } = {}): Promise<any[]> {
    const params = new URLSearchParams();
    
    if (options.owned) params.append('owned', 'true');
    if (options.membership) params.append('membership', 'true');
    if (options.starred) params.append('starred', 'true');
    if (options.simple) params.append('simple', 'true');
    if (options.per_page) params.append('per_page', options.per_page.toString());
    if (options.page) params.append('page', options.page.toString());

    const endpoint = `/projects?${params.toString()}`;
    const response = await this.makeRequest(endpoint);
    
    if (!response.ok) {
      throw new Error(`Failed to get projects: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Get a specific project by ID
   */
  async getProject(projectId: string | number): Promise<any> {
    const response = await this.makeRequest(`/projects/${projectId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to get project: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Get project merge requests
   */
  async getProjectMergeRequests(
    projectId: string | number,
    options: {
      state?: 'opened' | 'closed' | 'merged' | 'all';
      per_page?: number;
      page?: number;
    } = {}
  ): Promise<any[]> {
    const params = new URLSearchParams();
    
    if (options.state) params.append('state', options.state);
    if (options.per_page) params.append('per_page', options.per_page.toString());
    if (options.page) params.append('page', options.page.toString());

    const endpoint = `/projects/${projectId}/merge_requests?${params.toString()}`;
    const response = await this.makeRequest(endpoint);
    
    if (!response.ok) {
      throw new Error(`Failed to get merge requests: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Get merge request details
   */
  async getMergeRequest(projectId: string | number, mergeRequestIid: number): Promise<any> {
    const response = await this.makeRequest(`/projects/${projectId}/merge_requests/${mergeRequestIid}`);
    
    if (!response.ok) {
      throw new Error(`Failed to get merge request: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Get merge request changes/diff
   */
  async getMergeRequestChanges(projectId: string | number, mergeRequestIid: number): Promise<any> {
    const response = await this.makeRequest(`/projects/${projectId}/merge_requests/${mergeRequestIid}/changes`);
    
    if (!response.ok) {
      throw new Error(`Failed to get merge request changes: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Create a note/comment on a merge request
   */
  async createMergeRequestNote(
    projectId: string | number,
    mergeRequestIid: number,
    body: string
  ): Promise<any> {
    const response = await this.makeRequest(`/projects/${projectId}/merge_requests/${mergeRequestIid}/notes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ body }),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to create merge request note: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }

  /**
   * Get user's groups
   */
  async getGroups(options: {
    owned?: boolean;
    per_page?: number;
    page?: number;
  } = {}): Promise<any[]> {
    const params = new URLSearchParams();
    
    if (options.owned) params.append('owned', 'true');
    if (options.per_page) params.append('per_page', options.per_page.toString());
    if (options.page) params.append('page', options.page.toString());

    const endpoint = `/groups?${params.toString()}`;
    const response = await this.makeRequest(endpoint);
    
    if (!response.ok) {
      throw new Error(`Failed to get groups: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  }
}

/**
 * Factory function to create GitLab API client for a user
 */
export function createGitLabClient(userId: string): GitLabAPIClient {
  return new GitLabAPIClient(userId);
}
